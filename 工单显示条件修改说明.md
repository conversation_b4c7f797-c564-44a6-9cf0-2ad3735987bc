# 工单显示条件修改说明

## 需求描述
业主要求修改现有工单的显示条件：
- **原条件**：只有已授权的工单才显示
- **新条件**：已完成的工单无论是否授权都显示，其他状态的工单仍然只显示已授权的

## 工单状态说明
根据 `WorkOrderStatusEnum` 枚举类：
- 0: 待接单 (WAIT_TAKING)
- 1: 待盖章 (WAIT_HOSPITAL_CHECK)  
- 2: 待处理 (WAIT_PROCESSING)
- 3: 待回访 (WAIT_VISITING)
- **4: 已完成 (FINISHED)** ← 这是我们需要特殊处理的状态
- 5: 已拒绝 (REJECT)
- 6: 已延后 (DELAY)

## 修改的文件
`yudao-module-insurance/yudao-module-insurance-biz/src/main/resources/mapper/workorder2/WorkOrder2Mapper.xml`

## 修改的查询方法

### 1. selectPage2 (第12-31行)
**用途**: 工单分页查询
**修改内容**:
- 将 `inner join insurance_auth` 改为 `left join insurance_auth`
- 将条件 `iwo2.deleted = 0` 改为 `iwo2.deleted = 0 and (ia.id is not null or iwo2.status = 4)`

### 2. selectWorkOrderPage (第100-149行)
**用途**: 工单页面查询（主要的工单列表查询）
**修改内容**:
- 将条件 `and ia.id is not null` 改为 `and (ia.id is not null or iwo2.status = 4)`

### 3. selectList2 (第300-351行)
**用途**: Excel导出工单列表
**修改内容**:
- 将 `inner join insurance_auth` 改为 `left join insurance_auth`
- 添加条件 `and (ia.id is not null or iwo2.status = 4)`

### 4. selectDistinctHospitalNameList (第353-358行)
**用途**: 获取不重复的医院名称列表
**修改内容**:
- 将 `INNER JOIN insurance_auth` 改为 `LEFT JOIN insurance_auth`
- 添加条件 `AND (ia.id is not null or iwo2.status = 4)`

### 5. selectDistinctHospitalNameListWithAuth (第662-673行)
**用途**: 获取已授权的不重复医院名称列表
**修改内容**:
- 将 `INNER JOIN insurance_auth` 改为 `LEFT JOIN insurance_auth`
- 添加条件 `AND (ia.id is not null or iwo2.status = 4)`

## 修改逻辑说明
所有修改都遵循相同的逻辑：
```sql
-- 原条件（只显示已授权）
inner join insurance_auth as ia on iwo2.id_card_number = ia.idcard

-- 新条件（已授权 OR 已完成状态）
left join insurance_auth as ia on iwo2.id_card_number = ia.idcard
WHERE ... AND (ia.id is not null or iwo2.status = 4)
```

这样的修改确保：
1. **已授权的工单**：无论什么状态都会显示（ia.id is not null）
2. **未授权但已完成的工单**：也会显示（iwo2.status = 4）
3. **未授权且未完成的工单**：不会显示

## 影响范围
- 工单列表页面显示
- 工单Excel导出
- 医院名称下拉列表
- 工单分页查询API

## 测试建议
1. 验证已授权工单的各种状态都能正常显示
2. 验证未授权但已完成的工单能够显示
3. 验证未授权且未完成的工单不会显示
4. 验证Excel导出功能正常
5. 验证医院名称下拉列表包含已完成工单的医院
