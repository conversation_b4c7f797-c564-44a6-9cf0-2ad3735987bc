package cn.iocoder.yudao.module.insurance.service.wanda;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.insurance.dal.dataobject.wandaworkorder.WandaWorkOrderDO;
import cn.iocoder.yudao.module.insurance.dal.mysql.wandaworkorder.WandaWorkOrderMapper;
import cn.iocoder.yudao.module.insurance.service.wandaworkorder.WandaWorkOrderSyncManager;
import cn.iocoder.yudao.module.insurance.webservice.wanda.service.WandaQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Consumer;

@Service
@Slf4j
public class WandaWorkOrderSyncService {

    @Resource
    private WandaQueryService wandaQueryService;

    @Resource
    private WandaWorkOrderMapper wandaWorkOrderMapper;

    @Resource
    private DataSourceTransactionManager transactionManager;

    @Resource
    private WandaWorkOrderSyncManager syncManager;

    private static final int PAGE_SIZE = 50;

    public void syncWorkOrders(String startTime, String endTime, String taskId, int startPage) {
        log.info("[syncWorkOrders]开始同步万达工单数据,从第{}页开始", startPage);
        
        // 获取第一页数据，用于获取总页数
        String firstPageResult = wandaQueryService.queryInsuranceOrders(startTime, endTime, 1, PAGE_SIZE);
        JSONObject resultObj = JSONUtil.parseObj(firstPageResult);
        
        // 获取分页信息
        JSONObject header = resultObj.getJSONObject("root").getJSONObject("request").getJSONObject("header");
        int totalPages = header.getInt("pageSum");
        int totalRecords = header.getInt("records");
        
        log.info("[syncWorkOrders]总页数：{}，总记录数：{}", totalPages, totalRecords);
        
        // 设置总页数
        syncManager.setTaskTotalPages(taskId, totalPages);

        // 如果是继续同步,且起始页大于1,则跳过第一页数据处理
        boolean skipFirstPage = startPage > 1;

        // 遍历所有页面获取数据
        for (int pageNo = startPage; pageNo <= totalPages; pageNo++) {
            syncManager.renewTaskLock(taskId, 10); // 续期10分钟
            // 更新进度
            syncManager.updateTaskProgress(taskId, pageNo);

            String pageResult;
            if (!skipFirstPage && pageNo == 1) {
                // 如果是第一页且不需要跳过,直接使用之前查询的结果
                pageResult = firstPageResult;
            } else {
                // 否则重新查询数据
                pageResult = wandaQueryService.queryInsuranceOrders(startTime, endTime, pageNo, PAGE_SIZE);
            }
            
            List<WandaWorkOrderDO> workOrders = convertToWorkOrders(pageResult);
            if (CollUtil.isEmpty(workOrders)) {
                continue;
            }

            // 开启事务
            TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());
            try {
                // 保存或更新工单
                for (WandaWorkOrderDO workOrder : workOrders) {
                    saveOrUpdateWorkOrder(workOrder);
                }
                // 提交事务
                transactionManager.commit(status);
                log.info("[syncWorkOrders]同步第{}页数据完成，数量：{}", pageNo, workOrders.size());
            } catch (Exception e) {
                // 回滚事务
                transactionManager.rollback(status);
                log.error("[syncWorkOrders]同步第{}页数据失败", pageNo, e);
                throw new RuntimeException(String.format("同步第%d页数据失败", pageNo), e);
            }
        }
        
        log.info("[syncWorkOrders]同步万达工单数据完成");
    }

    private List<WandaWorkOrderDO> convertToWorkOrders(String jsonResult) {
        try {
            JSONObject root = JSONUtil.parseObj(jsonResult).getJSONObject("root");
            List<JSONObject> dataList = root.getJSONObject("request")
                    .getJSONObject("body")
                    .getJSONObject("dataList")
                    .getJSONArray("data").toList(JSONObject.class);

            List<WandaWorkOrderDO> workOrders = new ArrayList<>();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            for (JSONObject data : dataList) {
                WandaWorkOrderDO workOrder = WandaWorkOrderDO.builder()
                        .hospitalCode(data.getStr("HOSPITAL_CODE"))
                        .hospitalName(data.getStr("HOSPITAL_NAME"))
                        .hospitalLevel(data.getStr("HOSPITAL_LEVEL"))
                        .treatmentSerialNumberType(Integer.parseInt(data.getStr("TREATMENT_SERIAL_NUMBER_TYPE")))
                        .treatmentSerialNumber(data.getStr("TREATMENT_SERIAL_NUMBER"))
                        .name(data.getStr("NAME"))
                        .idCardNumber(data.getStr("ID_CARD_NUMBER"))
                        .address(data.getStr("ADDRESS"))
                        .mobilePhoneNumber(data.getStr("MOBILE_PHONE_NUMBER"))
                        .departmentCode(data.getStr("DEPARTMENT_CODE"))
                        .departmentName(data.getStr("DEPARTMENT_NAME"))
                        .doctorCode(data.getStr("DOCTOR_CODE"))
                        .doctorName(data.getStr("DOCTOR_NAME"))
                        .mainDiagnosisCode(data.getStr("MAIN_DIAGNOSIS_CODE"))
                        .mainDiagnosisName(data.getStr("MAIN_DIAGNOSIS_NAME"))
                        .otherDiagnosisCode(data.getStr("OTHER_DIAGNOSIS_CODE"))
                        .otherDiagnosisName(data.getStr("OTHER_DIAGNOSIS_NAME"))
                        .mainComplaint(data.getStr("MAIN_COMPLAINT"))
                        .currentMedicalHistory(data.getStr("CURRENT_MEDICAL_HISTORY"))
                        .pastMedicalHistory(data.getStr("PAST_MEDICAL_HISTORY"))
                        .geneticHistory(data.getStr("GENETIC_HISTORY"))
                        .hospitalizationNumber(data.getStr("HOSPITALIZATION_NUMBER"))
                        .contactPersonName(data.getStr("CONTACT_PERSON_NAME"))
                        .contactPersonPhoneNumber(data.getStr("CONTACT_PERSON_PHONE_NUMBER"))
                        .inpatientArea(data.getStr("INPATIENT_AREA"))
                        .bedNumber(data.getStr("BED_NUMBER"))
                        .complicationCode(data.getStr("COMPLICATION_CODE"))
                        .leaveHospitalState(data.getStr("LEAVE_HOSPITAL_STATE"))
                        .leaveHospitalDiagnosisName(data.getStr("LEAVE_HOSPITAL_DIAGNOSIS_NAME"))
                        .inHospitalDiagnosisName(data.getStr("IN_HOSPITAL_DIAGNOSIS_NAME"))
                        .leaveHospitalDescription(data.getStr("LEAVE_HOSPITAL_DESCRIPTION"))
                        .electronicBillIds(data.getStr("ELECTRONIC_BILL_IDS"))
                        .treatmentType(data.getStr("TREATMENT_TYPE"))
                        .orderType(Integer.parseInt(data.getStr("ORDER_TYPE")))
                        .status(0) // 设置初始状态为未处理
                        .build();

                // 处理日期字段
                parseDateTime(data, "TREATMENT_DATETIME", dateFormat, workOrder::setTreatmentDatetime);
                parseDateTime(data, "IN_HOSPITAL_DATETIME", dateFormat, workOrder::setInHospitalDatetime);
                parseDateTime(data, "LEAVE_HOSPITAL_DATETIME", dateFormat, workOrder::setLeaveHospitalDatetime);

                workOrders.add(workOrder);
            }

            return workOrders;
        } catch (Exception e) {
            log.error("[convertToWorkOrders]解析数据失败", e);
            throw new RuntimeException("解析工单数据失败", e);
        }
    }

    private void parseDateTime(JSONObject data, String field, SimpleDateFormat dateFormat, Consumer<Date> setter) {
        String dateStr = data.getStr(field);
        if (dateStr != null) {
            try {
                setter.accept(dateFormat.parse(dateStr));
            } catch (ParseException e) {
                log.warn("[parseDateTime]解析{}日期失败: {}", field, dateStr);
            }
        }
    }

    private void saveOrUpdateWorkOrder(WandaWorkOrderDO workOrder) {
        // 查询是否存在相同唯一键的记录
        WandaWorkOrderDO existingOrder = wandaWorkOrderMapper.selectByUniqueKey(
                workOrder.getOrderType(),
                workOrder.getIdCardNumber(),
                workOrder.getTreatmentSerialNumber(),
                workOrder.getTreatmentSerialNumberType(),
                workOrder.getHospitalCode()
        );
        
        if (existingOrder != null) {
            // 更新现有记录
            workOrder.setId(existingOrder.getId());
            // 保留原有的状态和失败原因
            workOrder.setStatus(existingOrder.getStatus());
            workOrder.setFailType(existingOrder.getFailType());
            workOrder.setFailReason(existingOrder.getFailReason());
            wandaWorkOrderMapper.updateById(workOrder);
            //输出原有的工单与现在工单的差异日志，只输出有差异的字段
            String diffMsg = diffMsg(existingOrder, workOrder);
            if (!diffMsg.isEmpty()) {
                log.info("[saveOrUpdateWorkOrder]工单id: {},差异信息：{}", workOrder.getId(), diffMsg);
            }
        } else {
            // 插入新记录
            wandaWorkOrderMapper.insert(workOrder);
        }
    }

    /**
     * 输出两个预处理工单的差异信息
     */
    private String diffMsg(WandaWorkOrderDO old, WandaWorkOrderDO newData) {
        //比较两个工单之间有内容差异的字段，输出差异字段的信息。若没有差异则为空字符串。可以用反射循环获取字段值
        StringBuilder diffMsg = new StringBuilder();
        Field[] fields = WandaWorkOrderDO.class.getDeclaredFields();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                Object oldValue = field.get(old);
                Object newValue = field.get(newData);
                if (!Objects.equals(oldValue, newValue)) {
                    diffMsg.append("字段: ").append(field.getName()).append(" 旧值: ").append(oldValue).append(" 新值: ").append(newValue).append("\n");
                }
            } catch (IllegalAccessException e) {
                log.error(e.getMessage());
            }
        }
        return diffMsg.toString();
    }
} 